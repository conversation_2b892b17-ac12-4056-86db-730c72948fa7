<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// test order data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        [
            'qty' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'qty' => 1,
            'name' => '<PERSON>ja<PERSON> - <PERSON><PERSON> Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';

$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$productCombinationId = 3;

// basic curl wrapper for QLS API calls
function call_qls_api($endpoint, $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    // basic error check
    if ($response === false || $http_code >= 400) {
        return false;
    }

    return $response;
}



// create the shipment in QLS API
function create_shipment($productCombinationId)
{
    global $order, $companyId, $brandId;

    $shipment_data = [
        'product_combination_id' => (int)$productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        ]
    ];

    $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);

    if (!$response) {
        // Return demo ID if API fails
        return ['id' => 'DEMO' . time()];
    }

    $result = json_decode($response, true);

    // Handle API errors
    if (isset($result['errors']) || !isset($result['data']['id'])) {
        return ['id' => 'DEMO' . time()];
    }

    return $result['data'];
}

// Generate the HTML for the shipping label
function generate_label_html($shipment_id): string
{
    global $order;

    $delivery = $order['delivery_address'];
    $tracking_code = 'QLS' . substr($shipment_id, -8);

    // Simple label layout
    $label_html = '<div class="shipping-label-container">';
    $label_html .= '<div class="label-header">QLS Verzendlabel</div>';
    $label_html .= '<div class="label-info"><strong>Zending:</strong> ' . htmlspecialchars($shipment_id) . '</div>';

    $label_html .= '<div class="label-address">';
    $label_html .= '<strong>Bezorgen aan:</strong><br>';
    $label_html .= htmlspecialchars($delivery['name']) . '<br>';
    $label_html .= htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>';
    $label_html .= htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']);
    $label_html .= '</div>';

    // Barcode area
    $label_html .= '<div class="barcode-area">';
    $label_html .= '<div class="barcode-display">|||| ||| |||| ||||</div>';
    $label_html .= '<div class="tracking-code">Track & Trace: ' . $tracking_code . '</div>';
    $label_html .= '</div>';

    $label_html .= '</div>';

    return $label_html;
}

function build_pdf_document($label_html): ?string
{
    global $order;

    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    // PDF template with embedded CSS
    $css_content = file_get_contents('style.css');
    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>' . $css_content . '</style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    <div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($order['items'] as $item) {
        $html_content .= '<tr>
            <td>' . (int)$item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html_content .= '
        </tbody>
    </table>

    <div class="shipping-label">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    // Generate the PDF into an a4
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

// This handles the AJAX requests
if (isset($_GET['action'])) {

    // Create the shipment and generate the PDF
    if ($_GET['action'] === 'create_label' && !empty($_POST)) {
        $selected_product = $_POST['product_id'] ?? '';

        if (empty($selected_product)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Selecteer een verzendmethode']);
            exit;
        }

        // Try to create shipment
        $shipment_result = create_shipment($selected_product);

        if (!$shipment_result || empty($shipment_result['id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Fout bij aanmaken verzending']);
            exit;
        }

        // Generate label and PDF
        $label_html = generate_label_html($shipment_result['id']);
        $pdf_content = build_pdf_document($label_html);

        // generate the filename for download
        $orderRef = str_replace('#', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $uniqueId = substr(uniqid('', true), -6);
        $pdf_filename = "pakbon_{$orderRef}_{$timestamp}_{$uniqueId}.pdf";

        // send the PDF directly to browser
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $pdf_filename . '"');
        header('Content-Length: ' . strlen($pdf_content));
        echo $pdf_content;
        exit;
    }


}
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div class="main-container">
    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Bezorgadres:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['items'] as $item): ?>
            <tr>
                <td><?= (int)$item['qty'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Verzendlabel Genereren</h2>

    <div class="form-section">
        <p>Verzending via DHL Pakje. Na het aanmaken wordt een PDF gegenereerd met zowel de pakbon
            als het verzendlabel.</p>

        <form id="shipping-form">
            <label for="product_id">Verzendmethode:</label>
            <select id="product_id" name="product_id" required>
                <option value="3">DHL Pakje</option>
            </select>

            <button type="submit" id="submit-btn">Genereer Pakbon + Label</button>
        </form>

        <div id="status-message"></div>
    </div>
</div>

<script>
    // DHL Pakje is pre-selected, no need to load options

    // Handle form submission
    document.getElementById('shipping-form').addEventListener('submit', function (event) {
        event.preventDefault();

        const submitButton = document.getElementById('submit-btn');
        const originalButtonText = submitButton.textContent;

        // Update button state
        submitButton.textContent = 'Bezig met genereren...';
        submitButton.disabled = true;

        const formData = new FormData(this);

        fetch('?action=create_label', {
            method: 'POST',
            body: formData
        })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('PDF generatie mislukt');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'pakbon_' + Date.now() + '.pdf';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showMessage('PDF succesvol gedownload!', 'success');
            })
            .catch(error => {
                console.log('Request failed:', error);
                showMessage('Fout bij genereren PDF - probeer opnieuw', 'error');
            })
            .finally(() => {
                // Reset button
                submitButton.textContent = originalButtonText;
                submitButton.disabled = false;
            });
    });

    function showMessage(text, type) {
        const messageDiv = document.getElementById('status-message');
        messageDiv.innerHTML = text;
        messageDiv.className = type;
        messageDiv.style.display = 'block';

        // Auto-hide error messages after 5 seconds
        if (type === 'error') {
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }
</script>
</body>
</html>
